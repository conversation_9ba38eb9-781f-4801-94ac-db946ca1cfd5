import cv2
from face_tracker import FaceTracker
from utils import draw_faces

def main():
    # 初始化摄像头并输出调试信息
    try:
        import sys
        print("尝试打开摄像头...")
        # 根据操作系统选择后端
        if sys.platform.startswith('win'):
            cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        else:
            cap = cv2.VideoCapture(0, cv2.CAP_V4L2)
        if not cap.isOpened():
            print("无法打开摄像头。请检查是否被其他程序占用，或尝试更换VideoCapture参数。")
            print(f"系统平台: {sys.platform}")
            print("尝试使用不同的 VideoCapture 参数或检查摄像头连接。")
            return
        print("摄像头已打开。")
        print(f"摄像头分辨率: {cap.get(cv2.CAP_PROP_FRAME_WIDTH)}x{cap.get(cv2.CAP_PROP_FRAME_HEIGHT)}")
        tracker = FaceTracker(method='mediapipe')
        while True:
            ret, frame = cap.read()
            if not ret or frame is None:
                print("无法读取摄像头画面，ret:", ret, ", frame:", frame)
                break
            faces = tracker.detect(frame)
            print(f"检测到{len(faces)}个人脸")
            frame = draw_faces(frame, faces)
            cv2.imshow('Face Tracking', frame)
            # 按q退出
            if cv2.waitKey(1) & 0xFF == ord('q'):
                break
    except Exception as e:
        import traceback
        print("程序发生异常：", e)
        traceback.print_exc()
    finally:
        try:
            tracker.release()
        except Exception:
            pass
        try:
            cap.release()
        except Exception:
            pass
        cv2.destroyAllWindows()

if __name__ == '__main__':
    main()
