import cv2

def draw_faces(frame, faces, color=(0,255,0), thickness=2):
    """
    在frame上绘制人脸框
    :param frame: 原始图像
    :param faces: 人脸框列表，每个为(x, y, w, h)
    :param color: 框颜色
    :param thickness: 线宽
    """
    for (x, y, w, h) in faces:
        # 计算人脸中心点
        cx = x + w // 2
        cy = y + h // 2
        # 打印中心点坐标
        print(f"人脸中心点坐标: ({cx}, {cy})")
        # 绘制红色中心点
        cv2.circle(frame, (cx, cy), 4, (0, 0, 255), -1)
        # 绘制黑色十字
        cross_len = max(10, min(w, h) // 4)
        cv2.line(frame, (cx - cross_len, cy), (cx + cross_len, cy), (0, 0, 0), 2)
        cv2.line(frame, (cx, cy - cross_len), (cx, cy + cross_len), (0, 0, 0), 2)
    return frame
