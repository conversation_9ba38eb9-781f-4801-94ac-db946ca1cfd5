# 人脸追踪项目

## 项目简介
本项目基于开源方案，使用电脑自带摄像头实现高效、精确且流畅的人脸追踪。

### 主要特性
- 兼容性强，支持主流Windows系统
- 采用高性能开源库（如OpenCV、dlib、mediapipe等）
- 充分注释，便于理解和修改
- 主程序入口为`main.py`

## 依赖安装
本项目依赖如下Python包：
- opencv-python
- mediapipe

安装方法：
```powershell
pip install opencv-python mediapipe
```

## 文件结构
- `main.py`：主程序入口，摄像头采集与人脸追踪逻辑
- `face_tracker.py`：封装人脸检测与追踪算法，兼容多种开源方案
- `utils.py`：工具函数
- `readme.md`：项目说明与分析

## 使用方法
1. 安装依赖
2. 运行`main.py`
3. 摄像头画面中检测到人脸时，中心会显示红点和黑色十字，控制台会输出中心点坐标。

```powershell
python main.py
```

## 代码分析
- `main.py`负责摄像头采集、画面显示与调用追踪模块。
- `face_tracker.py`兼容多种人脸检测与追踪算法，优先使用性能与精度兼顾的方案。
- `utils.py`提供辅助功能。当前版本已将人脸检测结果以“中心红点+黑色十字”显示在画面上，并在控制台实时打印人脸中心点坐标，不再显示传统方框。

## 性能与精度说明
- 默认采用mediapipe的人脸检测与追踪，兼顾速度与精度。
- 检测到的人脸仅以中心点红点和黑色十字显示，去除了传统方框，便于聚焦中心点追踪。
- 控制台会实时输出每个人脸的中心点坐标，便于调试和后续开发。
- 可根据需要切换为OpenCV/dlib等其他方案。

## 兼容性说明
- 仅依赖主流开源库，支持Windows主流Python环境

## 其他
如需扩展或修改算法，请参考代码注释。
